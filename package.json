{"name": "secure-file-vault", "version": "0.1.0", "private": true, "description": "Zero-knowledge, end-to-end encrypted file storage platform", "workspaces": ["apps/*", "packages/*", "libs/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev --concurrency=20", "dev:services": "docker compose up -d postgres redis", "dev:tools": "docker compose --profile tools up -d", "dev:full": "npm run dev:services && turbo run dev --concurrency=20", "lint": "turbo run lint", "lint:fix": "turbo run lint -- --fix", "test": "turbo run test", "test:watch": "turbo run test -- --watch", "test:coverage": "turbo run test -- --coverage", "test:e2e": "turbo run test:e2e", "type-check": "turbo run type-check", "clean": "turbo run clean && rm -rf node_modules", "clean:cache": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md,yml,yaml}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md,yml,yaml}\"", "db:setup": "docker compose up -d postgres && sleep 5 && npm run db:migrate", "db:migrate": "turbo run db:migrate", "db:reset": "turbo run db:reset", "db:studio": "turbo run db:studio", "services:up": "docker compose up -d", "services:down": "docker compose down", "services:logs": "docker compose logs -f", "prepare": "husky install"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.1.0", "turbo": "^1.11.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "packageManager": "npm@9.8.1", "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}}